package com.ydwl.common.core.domain.dto.transcode;

import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.Accessors;
import java.io.Serializable;
import java.util.List;

/**
 * 转码初始化响应VO
 *
 * <AUTHOR>
 * @since 2025-06-03
 */
@Data
@NoArgsConstructor
@Accessors(chain = true)
public class TranscodeInitResponseVo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 业务ID
     */
    private String bizId;

    /**
     * 任务ID
     */
    private String taskId;

    /**
     * 转码作业ID列表
     */
    private List<String> jobIds;

    /**
     * 转码状态
     */
    private String status;

    /**
     * 转码类型：FC、MTS
     */
    private String transcodeType;

    /**
     * 请求ID
     */
    private String requestId;
    
    /**
     * 作业ID（单个）
     */
    private String jobId;
    
    /**
     * 转码方法/方式
     */
    private String method;
    
    /**
     * 响应消息
     */
    private String message;

    /**
     * 错误码
     */
    private String errorCode;

    /**
     * 错误消息
     */
    private String errorMessage;

    /**
     * 输入文件信息
     */
    private InputInfo input;

    /**
     * 输出文件配置列表
     */
    private List<OutputConfig> outputs;

    /**
     * 用户数据
     */
    private String userData;

    /**
     * 预估转码时长（秒）
     */
    private Integer estimatedDuration;

    /**
     * 是否可重试
     */
    private Boolean retryable;

    /**
     * 判断转码是否成功
     * 根据状态和错误码判断转码是否成功
     */
    public boolean isSuccess() {
        // 如果有错误码，则认为失败
        if (errorCode != null && !errorCode.trim().isEmpty()) {
            return false;
        }

        // 根据状态判断
        if (status != null) {
            String statusLower = status.toLowerCase();
            return "success".equals(statusLower) ||
                   "completed".equals(statusLower) ||
                   "submitted".equals(statusLower) ||
                   "1".equals(status); // 数字1表示成功
        }

        // 如果有任务ID或作业ID，认为提交成功
        return (taskId != null && !taskId.trim().isEmpty()) ||
               (jobId != null && !jobId.trim().isEmpty()) ||
               (jobIds != null && !jobIds.isEmpty());
    }

    /**
     * 输入文件信息
     */
    @Data
    public static class InputInfo implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 输入文件OSS存储桶
         */
        private String bucket;

        /**
         * 输入文件区域位置
         */
        private String location;

        /**
         * 输入文件OSS对象路径
         */
        private String object;
    }

    /**
     * 输出文件配置
     */
    @Data
    public static class OutputConfig implements Serializable {
        private static final long serialVersionUID = 1L;

        /**
         * 输出文件OSS存储桶
         */
        private String bucket;

        /**
         * 输出文件区域位置
         */
        private String location;

        /**
         * 输出文件OSS对象路径
         */
        private String object;

        /**
         * 转码模板ID
         */
        private String templateId;

        /**
         * 转码作业ID
         */
        private String jobId;
    }
}
