package com.ydwl.common.core.domain.dto.transcode;

import cn.idev.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serial;
import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @description 转码模板信息 DTO
 * @date 2024/6/5 15:58
 */
@Data
@NoArgsConstructor
public class TranscodeTemplateInfo implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    /**
     * 模板ID
     */
    private Long id;

    /**
     * 模板名称
     */
    private String name;

    /**
     * 容器格式
     */
    private String container;

    /**
     * 视频编码
     */
    private String videoCodec;

    /**
     * 视频码率
     */
    private Integer videoBitrate;

    /**
     * 视频帧率
     */
    private Integer videoFramerate;

    /**
     * 视频宽度
     */
    private Integer videoWidth;

    /**
     * 视频高度
     */
    private Integer videoHeight;

    /**
     * 音频编码
     */
    private String audioCodec;

    /**
     * 音频码率
     */
    private Integer audioBitrate;

    /**
     * 音频采样率
     */
    private Integer audioSamplerate;

    /**
     * 音频声道
     */
    private Integer audioChannels;

    /**
     * 分辨率标识（如：480p, 720p, 1080p）
     */
    private String resolution;

    /**
     * 获取分辨率标识
     * 根据视频宽高自动计算分辨率标识
     */
    public String getResolution() {
        if (resolution != null && !resolution.trim().isEmpty()) {
            return resolution;
        }

        // 根据视频高度自动判断分辨率
        if (videoHeight != null) {
            if (videoHeight <= 480) {
                return "480p";
            } else if (videoHeight <= 720) {
                return "720p";
            } else if (videoHeight <= 1080) {
                return "1080p";
            } else if (videoHeight <= 1440) {
                return "2k";
            } else {
                return "4k";
            }
        }

        // 默认返回720p
        return "720p";
    }
}
