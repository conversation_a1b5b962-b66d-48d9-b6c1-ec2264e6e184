package com.ydwl.LiveTranscoding.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.context.annotation.EnableAspectJAutoProxy;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.client.SimpleClientHttpRequestFactory;

/**
 * 转码优化配置类
 * 自动配置所有优化相关的服务组件
 *
 * <AUTHOR>
 * @since 2024-01-01
 */
@Slf4j
@Configuration
@EnableAspectJAutoProxy
@EnableRetry
public class TranscodeOptimizationConfig {

    /**
     * RestTemplate Bean
     * 为回调重试服务提供HTTP客户端支持
     */
    @Bean
    @ConditionalOnMissingBean
    public RestTemplate restTemplate() {
        log.info("初始化RestTemplate");
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(10000); // 10秒连接超时
        factory.setReadTimeout(30000);    // 30秒读取超时
        RestTemplate restTemplate = new RestTemplate(factory);
        return restTemplate;
    }
}
