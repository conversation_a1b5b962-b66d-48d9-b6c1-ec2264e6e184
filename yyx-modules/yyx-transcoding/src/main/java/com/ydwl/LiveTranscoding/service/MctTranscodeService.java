package com.ydwl.LiveTranscoding.service;

import com.alibaba.fastjson.JSON;
import com.aliyun.mts20140618.Client;
import com.aliyun.mts20140618.models.SubmitJobsRequest;
import com.aliyun.mts20140618.models.SubmitJobsResponse;
import com.aliyun.mts20140618.models.SubmitJobsResponseBody;
import com.ydwl.LiveTranscoding.config.AliyunService;
import com.ydwl.LiveTranscoding.config.GlobalConfigProperties;
import com.ydwl.LiveTranscoding.domain.dto.MtsInput;
import com.ydwl.LiveTranscoding.domain.dto.MtsOutputItemDto;
import com.ydwl.LiveTranscoding.domain.dto.MtsTranscodeRequestDto;
import com.ydwl.common.core.domain.dto.transcode.TranscodeInitResponseVo;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * 阿里云媒体转码服务(MTS)处理类
 * 用于处理视频转码、水印添加等媒体处理任务
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class MctTranscodeService {

    private final GlobalConfigProperties config;
    private final TranscodeTemplateService templateService;
    private final TranscodeErrorHandler errorHandler;
    private final CallbackRetryService callbackRetryService;
//
//    @Value("${transcoding.mts.default.output-bucket:video-ydwl}")
//    private String defaultOutputBucket;
//
//    @Value("${transcoding.mts.default.location:oss-cn-beijing}")
//    private String defaultLocation;
//
//    @Value("${transcoding.mts.default.pipeline-id:YOUR_DEFAULT_MTS_PIPELINE_ID}")
//    private String defaultPipelineId;
//
//    @Value("${transcoding.mts.default.referer:*}")
//    private String defaultReferer;


    /**
     * 执行MTS视频转码任务
     *
     * @param dto 转码任务参数
     * @return 转码任务响应信息
     * @throws Exception 转码过程中可能发生的异常
     */
    public TranscodeInitResponseVo MtsTranscode(MtsTranscodeRequestDto dto) throws Exception {
        validateTranscodeParams(dto);
        log.info("开始执行MTS转码任务，BizId: {}, InputObject: {}", dto.getBizId(), dto.getInput().getObject());
        Client mtsClient = new AliyunService().getMtsClient();
        SubmitJobsRequest submitJobsRequest = new SubmitJobsRequest();
        submitJobsRequest.setOutputBucket(dto.getOutputBucket() != null ? dto.getOutputBucket() : config.getTranscode().getMts().getOssBucketName());
        submitJobsRequest.setOutputLocation(dto.getOutputLocation() != null ? dto.getOutputLocation() : config.getTranscode().getMts().getLocation());
        submitJobsRequest.setPipelineId(dto.getPipelineId() != null ? dto.getPipelineId() : config.getTranscode().getMts().getPipeline());

        String inputJson = buildInputJson(dto.getInput());
        submitJobsRequest.setInput(inputJson);

        String outputsJson = buildOutputsJson(dto.getOutputs(), dto.getBizId());
        submitJobsRequest.setOutputs(outputsJson);

        // 验证回调URL
        if (StringUtils.hasText(dto.getCallbackUrl())) {
            if (!callbackRetryService.validateCallbackUrl(dto.getCallbackUrl())) {
                throw errorHandler.createTranscodingException(
                    "无效的回调URL: " + dto.getCallbackUrl(), dto.getBizId(), "mts");
            }
            log.warn("如果MTS转码不设置回调地址。将依赖管道配置的回调。 URL: {}", dto.getCallbackUrl());
        }

        try {
            log.info("提交MTS转码任务，BizId: {}, 输入: {}/{}, 输出数量: {}",
                    dto.getBizId(), dto.getInput().getBucket(), dto.getInput().getObject(), dto.getOutputs().size());

            SubmitJobsResponse submitJobsResponse = mtsClient.submitJobs(submitJobsRequest);
            SubmitJobsResponseBody body = submitJobsResponse.getBody();
            String requestId = body != null ? body.getRequestId() : "N/A";
            String jobId = "N/A";
            if (body != null && body.getJobResultList() != null && !body.getJobResultList().getJobResult().isEmpty()) {
                jobId = body.getJobResultList().getJobResult().get(0).getJob().getJobId();
            }
            log.info("MTS转码任务提交成功，BizId: {}, MTS JobId: {}, RequestId: {}, 状态码：{}",
                    dto.getBizId(), jobId, requestId, submitJobsResponse.getStatusCode());

            // 发送开始回调（如果启用）
                try {
                    callbackRetryService.sendCallbackAsync(dto.getCallbackUrl(),
                        callbackRetryService.buildCallbackData(jobId, dto.getBizId(), "STARTED", null, "mts"), dto.getBizId());
                } catch (Exception callbackEx) {
                    log.warn("发送MTS转码开始回调失败，BizId: {}, JobId: {}", dto.getBizId(), jobId, callbackEx);
                }

            return buildResponse(submitJobsResponse, dto.getBizId());
        } catch (Exception e) {
            log.error("MTS转码任务提交失败，BizId: {}：", dto.getBizId(), e);

            // 使用错误处理器处理异常
            TranscodeErrorHandler.ErrorHandleResult errorResult = errorHandler.handleError(e, dto.getBizId(), "mts");
                try {
                    callbackRetryService.sendCallbackAsync(dto.getCallbackUrl(),
                        callbackRetryService.buildCallbackData(null, dto.getBizId(), "FAILED", errorResult.errorMessage(), "mts"), dto.getBizId());
                } catch (Exception callbackEx) {
                    log.warn("发送MTS转码失败回调失败，BizId: {}", dto.getBizId(), callbackEx);
                }


            throw errorHandler.createTranscodingException(
                "MTS转码任务提交失败: " + errorResult.errorMessage(), dto.getBizId(), "mts", e);
        }
    }

    /**
     * 使用模板ID进行转码
     *
     * @param bucket 输入存储桶
     * @param object 输入文件路径
     * @param userData 用户数据（bizId）
     * @param callbackUrl 回调地址
     * @param templateIds 转码模板ID列表
     * @param segmentTime HLS分片时间
     * @return 转码响应
     * @throws Exception 调用异常
     */
    public TranscodeInitResponseVo transcodeWithTemplates(String bucket, String object,
                                                         String userData, String callbackUrl,
                                                         List<String> templateIds, Integer segmentTime) throws Exception {

        log.info("使用模板进行MTS转码，UserData: {}, Bucket: {}, Object: {}, 模板: {}",
                userData, bucket, object, templateIds);

        // 构建MTS转码请求
        MtsTranscodeRequestDto dto = new MtsTranscodeRequestDto();
        dto.setBizId(userData);
        dto.setCallbackUrl(callbackUrl);

        // 设置输入
        MtsInput input = new MtsInput();
        input.setBucket(bucket);
        input.setObject(object);
        input.setLocation(config.getTranscode().getMts().getLocation());
        input.setReferer("*");
        dto.setInput(input);

        // 设置输出
        List<MtsOutputItemDto> outputs = new ArrayList<>();
        for (int i = 0; i < templateIds.size(); i++) {
            String templateId = templateIds.get(i);
            MtsOutputItemDto output = new MtsOutputItemDto();
            output.setTemplateId(templateId);

            // 生成输出文件名
            String fileName = extractFileNameWithoutExtension(object);
            String outputPath = "output/" + fileName + "_" + templateId + ".m3u8";
            output.setOutputObject(outputPath);
            output.setUserData(userData + "_output_" + i);

            outputs.add(output);
        }
        dto.setOutputs(outputs);

        // 设置其他参数
        dto.setOutputBucket(config.getTranscode().getMts().getOssBucketName());
        dto.setOutputLocation(config.getTranscode().getMts().getLocation());
        dto.setPipelineId(config.getTranscode().getMts().getPipeline());

        return MtsTranscode(dto);
    }

    /**
     * 从文件路径中提取不带扩展名的文件名
     */
    private String extractFileNameWithoutExtension(String objectPath) {
        String fileName = objectPath;
        if (fileName.contains("/")) {
            fileName = fileName.substring(fileName.lastIndexOf("/") + 1);
        }
        if (fileName.contains(".")) {
            fileName = fileName.substring(0, fileName.lastIndexOf("."));
        }
        return fileName;
    }

    /**
     * 验证转码参数
     *
     * @param dto 转码任务参数
     * @throws IllegalArgumentException 参数验证失败时抛出
     */
    private void validateTranscodeParams(MtsTranscodeRequestDto dto) {
        if (dto == null) {
            throw new IllegalArgumentException("转码参数不能为空");
        }
        if (dto.getInput() == null) {
            throw new IllegalArgumentException("输入源配置不能为空");
        }
        if (!StringUtils.hasText(dto.getInput().getObject())) {
            throw new IllegalArgumentException("输入文件路径不能为空");
        }
        if (!StringUtils.hasText(dto.getInput().getBucket())) {
            throw new IllegalArgumentException("输入文件存储桶不能为空");
        }
        if (!StringUtils.hasText(dto.getInput().getLocation())) {
            throw new IllegalArgumentException("输入文件位置不能为空");
        }
        if (!StringUtils.hasText(dto.getBizId())) {
            throw new IllegalArgumentException("业务ID (bizId) 不能为空");
        }
        if (dto.getOutputs() == null || dto.getOutputs().isEmpty()) {
            throw new IllegalArgumentException("输出配置列表不能为空");
        }
        for (MtsOutputItemDto output : dto.getOutputs()) {
            if (!StringUtils.hasText(output.getOutputObject())) {
                throw new IllegalArgumentException("输出配置中的OutputObject不能为空");
            }
            if (!StringUtils.hasText(output.getTemplateId())) {
                throw new IllegalArgumentException("输出配置中的TemplateId不能为空");
            }
        }
    }

    /**
     * 构建输入源JSON配置
     *
     * @param inputDto 输入源参数
     * @return 输入源JSON字符串
     */
    private String buildInputJson(MtsInput inputDto) {
        try {
            Map<String, String> inputMap = new HashMap<>();
            inputMap.put("Bucket", inputDto.getBucket());
            inputMap.put("Location", inputDto.getLocation());
            inputMap.put("Object", inputDto.getObject());
            inputMap.put("Referer", inputDto.getReferer() != null ? inputDto.getReferer() : "*");
            return JSON.toJSONString(inputMap);
        } catch (Exception e) {
            log.error("构建输入JSON失败：", e);
            throw new RuntimeException("构建输入JSON失败：" + e.getMessage());
        }
    }
    /**
     * 构建HLS格式的输出配置JSON
     *
     * @param jobBizId 转码任务参数
     * @return 输出配置JSON字符串
     */
    private String buildOutputsJson(List<MtsOutputItemDto> outputs, String jobBizId) {
        List<Map<String, Object>> outputList = new ArrayList<>();
        for (MtsOutputItemDto item : outputs) {
            Map<String, Object> outputMap = new HashMap<>();
            outputMap.put("OutputObject", item.getOutputObject());
            outputMap.put("TemplateId", item.getTemplateId());

            outputMap.put("UserData", StringUtils.hasText(item.getUserData()) ? item.getUserData() : jobBizId);

            if (item.getOutputObject() != null && item.getOutputObject().toLowerCase().endsWith(".m3u8")) {
                Map<String, Object> segmentConfig = new HashMap<>();
                segmentConfig.put("SegmentFormat", "ts");
                segmentConfig.put("SegmentDuration", "10");
                outputMap.put("SegmentConfig", segmentConfig);
            }
            outputList.add(outputMap);
        }
        return JSON.toJSONString(outputList);
    }

    /**
     * 构建响应信息
     *
     * @param response MTS转码任务响应
     * @param bizId 业务ID
     * @return 转码任务响应信息
     */
    private TranscodeInitResponseVo buildResponse(SubmitJobsResponse response, String bizId) {
        String jobId = "N/A";
        String message = "转码任务已提交";

        // 从响应中提取jobId和状态信息
        if (response != null && response.getBody() != null) {
            try {
                if (response.getBody().getJobResultList() != null &&
                    !response.getBody().getJobResultList().getJobResult().isEmpty()) {

                    var firstJob = response.getBody().getJobResultList().getJobResult().get(0);
                    if (firstJob.getJob() != null) {
                        jobId = firstJob.getJob().getJobId();

                        if (firstJob.getSuccess()) {
                            message = "转码任务已提交，MTS JobId：" + jobId;
                        } else {
                            message = "转码任务提交可能存在问题，MTS JobId：" + jobId +
                                    ", Code: " + firstJob.getCode() +
                                    ", Message: " + firstJob.getMessage();
                        }
                    } else {
                        message = "转码任务提交响应中未包含有效的Job信息";
                        if (firstJob.getCode() != null) {
                            message += ", Code: " + firstJob.getCode();
                        }
                        if (firstJob.getMessage() != null) {
                            message += ", Message: " + firstJob.getMessage();
                        }
                    }
                } else {
                    message = "转码任务提交成功，但响应数据中JobResultList为空或不存在";
                    if (response.getBody().getRequestId() != null) {
                        message += "，RequestId: " + response.getBody().getRequestId();
                    }
                }
            } catch (Exception e) {
                log.warn("解析MTS响应时发生异常: {}", e.getMessage());
                message = "转码任务提交成功，但解析响应数据时出错: " + e.getMessage();
            }
        } else {
            message = "转码任务提交成功，但响应体为空";
        }

        // 创建响应对象
        TranscodeInitResponseVo responseVo = new TranscodeInitResponseVo();
        responseVo.setStatus(String.valueOf(1L)); // 1表示任务已提交
        responseVo.setBizId(bizId);
        responseVo.setJobId(jobId);
        responseVo.setMethod("mts");
        responseVo.setMessage(message);

        return responseVo;
    }
}
