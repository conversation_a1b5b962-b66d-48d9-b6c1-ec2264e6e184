package com.ydwl.live.config;

import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.retry.annotation.EnableRetry;
import org.springframework.web.client.RestTemplate;
import org.springframework.http.client.SimpleClientHttpRequestFactory;

/**
 * 直播应用配置类
 * 提供应用所需的基础Bean配置
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Slf4j
@Configuration
@EnableRetry
public class LiveApplicationConfig {

    /**
     * RestTemplate Bean
     * 为HTTP客户端提供支持
     */
    @Bean
    @ConditionalOnMissingBean
    public RestTemplate restTemplate() {
        log.info("初始化RestTemplate");
        SimpleClientHttpRequestFactory factory = new SimpleClientHttpRequestFactory();
        factory.setConnectTimeout(10000); // 10秒连接超时
        factory.setReadTimeout(30000);    // 30秒读取超时
        return new RestTemplate(factory);
    }
}
