package com.ydwl.live.service.impl;

import com.alibaba.fastjson.JSON;
import com.aliyun.oss.OSS;
import com.aliyuncs.DefaultAcsClient;
import com.aliyuncs.auth.sts.AssumeRoleRequest;
import com.aliyuncs.auth.sts.AssumeRoleResponse;
import com.aliyuncs.exceptions.ClientException;
import com.aliyuncs.profile.DefaultProfile;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.ydwl.LiveTranscoding.config.GlobalConfigProperties;
import com.ydwl.common.core.constant.CacheNames;
import com.ydwl.common.core.domain.dto.transcode.TranscodeInitResponseVo;
import com.ydwl.common.core.domain.dto.transcode.TranscodeRequestVo;
import com.ydwl.common.core.domain.dto.transcode.TranscodeTemplateInfo;
import com.ydwl.common.core.exception.ServiceException;
import com.ydwl.common.core.service.IUniversalTranscodeService;
import com.ydwl.common.core.service.M3u8Service;
import com.ydwl.common.core.utils.DateUtils;
import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.core.utils.file.FileUtils;
import com.ydwl.common.oss.core.OssClient;
import com.ydwl.common.oss.entity.UploadResult;
import com.ydwl.common.oss.enums.AccessPolicyType;
import com.ydwl.common.oss.factory.OssFactory;
import com.ydwl.common.oss.properties.OssProperties;
import com.ydwl.common.redis.utils.CacheUtils;
import com.ydwl.common.redis.utils.RedisUtils;
import com.ydwl.live.callback.enums.UploadStatusEnum;
import com.ydwl.live.domain.LiveTranscodeTemplate;
import com.ydwl.live.domain.LiveVideoUpload;
import com.ydwl.live.domain.bo.ConfirmUploadBo;
import com.ydwl.live.domain.bo.LiveTranscodeTaskBo;
import com.ydwl.live.domain.bo.LiveVideoUploadBo;
import com.ydwl.live.domain.bo.PreSignedUrlRequestBo;
import com.ydwl.live.domain.vo.LiveVideoUploadVo;
import com.ydwl.live.domain.vo.OssPreSignedUrlVo;
import com.ydwl.live.mapper.LiveVideoUploadMapper;
import com.ydwl.live.service.ILiveOssService;
import com.ydwl.live.service.ILiveTranscodeTaskService;
import com.ydwl.live.service.ILiveTranscodeTemplateService;
import com.ydwl.live.util.TranscodeTemplateConverter;

import jakarta.validation.constraints.NotNull;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FilenameUtils;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.annotation.Primary;
import org.springframework.stereotype.Service;
import org.springframework.web.multipart.MultipartFile;

import javax.crypto.Mac;
import javax.crypto.spec.SecretKeySpec;
import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.security.InvalidKeyException;
import java.security.MessageDigest;
import java.security.NoSuchAlgorithmException;
import java.time.Duration;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 文件上传 服务层实现
 *
 * <AUTHOR> Yi
 */
@Slf4j
@RequiredArgsConstructor
@Service
@Primary
public  class LiveOssServiceImpl implements ILiveOssService, M3u8Service {

    private final LiveVideoUploadMapper baseMapper;
    private final LiveVideoUploadServiceImpl liveVideoUploadService;
    private final ILiveTranscodeTaskService liveTranscodeTaskService;
    private final ILiveTranscodeTemplateService liveTranscodeTemplateService;
    private final ApplicationEventPublisher applicationEventPublisher;
    private final IUniversalTranscodeService universalTranscodeService;
    private final GlobalConfigProperties config;

    // 支持的视频文件类型
    private static final String[] ALLOWED_VIDEO_TYPES = {
        "video/mp4", "video/avi", "video/mov", "video/wmv", "video/flv", "video/mkv"
    };

//     默认最大文件大小：2GB
     private static final Long DEFAULT_MAX_FILE_SIZE = 2L * 1024 * 1024 * 1024;

    // OSS回调URL
    // private static final String OSS_CALLBACK_URL = "https://yd-live.cpolar.cn/live/videoUpload/ossCallback";


    /**
     * 方案一：上传 MultipartFile 到对象存储服务，并保存文件信息到数据库
     *
     * @param file 要上传的 MultipartFile 对象
     * @return 上传成功后的 LiveVideoUploadVo 对象，包含文件信息
     * @throws ServiceException 如果上传过程中发生异常，则抛出 ServiceException 异常
     */
    @Override
    public LiveVideoUploadVo upload(MultipartFile file, long id) {
        // 文件名称4_1747983979.mp4
        String originalfileName = file.getOriginalFilename();
        // 文件格式 .mp4
        String suffix = StringUtils.substring(originalfileName, originalfileName.lastIndexOf("."), originalfileName.length());
        OssClient storage = OssFactory.instance();
        UploadResult uploadResult;
        try {
            uploadResult = storage.uploadSuffix(file.getBytes(), suffix, file.getContentType());
        } catch (IOException e) {
            throw new ServiceException(e.getMessage());
        }
        // 保存文件信息
        return buildResultEntity(originalfileName, suffix, storage.getConfigKey(), uploadResult, id);
    }

    /**
     * 方案二：获取OSS预签名上传URL
     *
     * @param request 预签名URL请求对象
     * @return 预签名上传信息
     */
    @Override
    public OssPreSignedUrlVo getPreSignedUploadUrl(PreSignedUrlRequestBo request) {
        try {
            // 验证文件类型
            if (!isValidVideoType(request.getContentType())) {
                throw new ServiceException("不支持的文件类型：" + request.getContentType());
            }

            OssClient storage = OssFactory.instance("aliyun-video-ydwl");

            // 生成唯一的对象键
            String suffix = StringUtils.substring(request.getFileName(), request.getFileName().lastIndexOf("."), request.getFileName().length());
            String dateStr = LocalDateTime.now().format(DateTimeFormatter.ofPattern("yyyy/MM/dd"));
            String objectKey = String.format("live-videos/%d/%s/%s%s",
            request.getLiveId(), dateStr, UUID.randomUUID().toString(), suffix);

            // 设置过期时间为1小时
            long expirationTime = System.currentTimeMillis() / 1000 + 3600;

            // 构建预签名上传信息
            OssPreSignedUrlVo preSignedUrlVo = new OssPreSignedUrlVo();
            preSignedUrlVo.setObjectKey(objectKey);
            preSignedUrlVo.setExpirationTime(expirationTime);
            preSignedUrlVo.setMaxFileSize(request.getMaxFileSize() != null ? request.getMaxFileSize() :DEFAULT_MAX_FILE_SIZE );
            preSignedUrlVo.setAllowedContentTypes(ALLOWED_VIDEO_TYPES);

            // 设置表单字段（用于OSS PostObject上传）
            Map<String, String> formFields = new HashMap<>();
            formFields.put("key", objectKey);
            formFields.put("Content-Type", request.getContentType());
            formFields.put("success_action_status", "200");

            // 添加文件大小限制策略
            String policy = buildUploadPolicy(objectKey, request.getContentType(),
                preSignedUrlVo.getMaxFileSize(), expirationTime);
            formFields.put("policy", policy);

            String signature = generateSignature(policy, storage);
            formFields.put("signature", signature);
            formFields.put("OSSAccessKeyId", getAccessKeyId(storage));

            // 添加OSS回调参数
            Map<String, Object> callbackParams = new HashMap<>();
            callbackParams.put("callbackUrl", config.getOss().getCallbackUrl());
            callbackParams.put("callbackBody", "{\"objectKey\":${object},\"size\":${size},\"mimeType\":${mimeType},\"liveId\":" + request.getLiveId() + ",\"fileName\":\"" + request.getFileName() + "\",\"contentType\":\"" + request.getContentType() + "\"}");
            callbackParams.put("callbackBodyType", "application/json");
            callbackParams.put("callbackSNI", true);
            String callbackJson = JSON.toJSONString(callbackParams);
            String callbackBase64 = java.util.Base64.getEncoder().encodeToString(callbackJson.getBytes());
            formFields.put("callback", callbackBase64);

            preSignedUrlVo.setFormFields(formFields);

            // 构建上传URL
            String uploadUrl = String.format("https://%s.%s",
                getBucketName(storage), getEndpoint(storage));
            preSignedUrlVo.setUploadUrl(uploadUrl);
            // 设置回调URL（可选）
            preSignedUrlVo.setCallbackUrl(config.getOss().getCallbackUrl());

            // 构建文件URL
            String fileUrl = String.format("https://%s.%s/%s",
                getBucketName(storage), getEndpoint(storage), objectKey);

            // 创建上传记录，设置状态为上传中
            try {
                LiveVideoUploadBo oss = new LiveVideoUploadBo();
                oss.setOssUrl(fileUrl);
                oss.setFileType(suffix);
                oss.setFileName(request.getFileName());
                oss.setLiveId(request.getLiveId());
                // 预估文件大小
                oss.setFileSizeBytes(request.getMaxFileSize() != null ? request.getMaxFileSize() : DEFAULT_MAX_FILE_SIZE);

                // 保存前端传入的视频参数
                oss.setVideoDurationSeconds(request.getVideoDurationSeconds());
                oss.setVideoResolution(request.getVideoResolution());
                oss.setVideoBitrateKbps(request.getVideoBitrateKbps());
                oss.setFrameRate(request.getFrameRate());
                oss.setVideoCodec(request.getVideoCodec());
                oss.setAudioCodec(request.getAudioCodec());
                oss.setAspectRatio(request.getAspectRatio());
                oss.setCreatedDate(request.getCreatedDate());
                oss.setLastModifiedDate(request.getLastModifiedDate());

                oss.setUploadStatus(UploadStatusEnum.UPLOADING.getCode()); // 上传中

                boolean saved = liveVideoUploadService.insertByBo(oss);
                if (saved) {
                    log.info("创建上传记录成功，状态设置为上传中，liveId: {}, objectKey: {}, 视频参数: 分辨率={}, 码率={}kbps",
                        request.getLiveId(), objectKey, request.getVideoResolution(), request.getVideoBitrateKbps());
                } else {
                    log.warn("创建上传记录失败，liveId: {}, objectKey: {}", request.getLiveId(), objectKey);
                }
            } catch (Exception e) {
                log.error("创建上传记录失败，但不影响获取预签名URL", e);
                // 创建记录失败不影响获取预签名URL
            }

            return preSignedUrlVo;

        } catch (Exception e) {
            log.error("生成预签名上传URL失败", e);
            throw new ServiceException("生成预签名上传URL失败: " + e.getMessage());
        }
    }

    /**
     * 方案二：确认文件上传完成，保存文件信息到数据库
     *
     * @param confirmUploadBo 确认上传请求对象
     * @return 上传记录
     */
    @Override
    public LiveVideoUploadVo confirmUpload(ConfirmUploadBo confirmUploadBo) {
        try {
            OssClient storage = OssFactory.instance("aliyun-video-ydwl");

            // 验证文件是否真的存在于OSS中
            // boolean exists = storage.doesObjectExist(confirmUploadBo.getObjectKey());
            // if (!exists) {
            //     throw new ServiceException("文件在OSS中不存在，上传可能失败");
            // }
            // 获取文件后缀
            String suffix = StringUtils.substring(confirmUploadBo.getFileName(),
                confirmUploadBo.getFileName().lastIndexOf("."), confirmUploadBo.getFileName().length());

            // 构建文件URL
            String fileUrl = String.format("https://mps.play.ycyyx.com/%s", confirmUploadBo.getObjectKey());

            // 先查询是否已存在相同ObjectKey的记录
            LiveVideoUploadBo queryBo = new LiveVideoUploadBo();
            queryBo.setOssUrl(fileUrl);
            List<LiveVideoUploadVo> existingRecords = liveVideoUploadService.queryList(queryBo);

            LiveVideoUploadBo oss;
            boolean isUpdate = false;
            LiveVideoUploadVo existingRecord = null;

            if (existingRecords != null && !existingRecords.isEmpty()) {
                // 找到已存在的记录，进行更新
                existingRecord = existingRecords.get(0);
                oss = new LiveVideoUploadBo();
                oss.setId(existingRecord.getId());
                isUpdate = true;
                log.info("找到已存在的上传记录，进行更新，ID: {}", oss.getId());
            } else {
                // 没有找到记录，创建新记录
                oss = new LiveVideoUploadBo();
                log.info("未找到已存在的上传记录，创建新记录");
            }

            // 设置文件信息
            oss.setOssUrl(fileUrl);
            oss.setFileType(suffix);
            oss.setFileName(confirmUploadBo.getFileName());
            oss.setLiveId(confirmUploadBo.getLiveId());
            oss.setFileSizeBytes(confirmUploadBo.getFileSize());
            oss.setFileMd5(confirmUploadBo.getFileMd5());

            // 处理视频参数，优先使用回调传入的值，如果为null则保留已有值
            if (isUpdate) {
                // 如果是更新已存在的记录，保留原有值（如果新值为null）
                oss.setVideoDurationSeconds(confirmUploadBo.getVideoDurationSeconds() != null ?
                    confirmUploadBo.getVideoDurationSeconds() : existingRecord.getVideoDurationSeconds());

                oss.setVideoResolution(confirmUploadBo.getVideoResolution() != null ?
                    confirmUploadBo.getVideoResolution() : existingRecord.getVideoResolution());

                oss.setVideoBitrateKbps(confirmUploadBo.getVideoBitrateKbps() != null ?
                    confirmUploadBo.getVideoBitrateKbps() : existingRecord.getVideoBitrateKbps());

                oss.setFrameRate(confirmUploadBo.getFrameRate() != null ?
                    confirmUploadBo.getFrameRate() : existingRecord.getFrameRate());

                oss.setVideoCodec(confirmUploadBo.getVideoCodec() != null ?
                    confirmUploadBo.getVideoCodec() : existingRecord.getVideoCodec());

                oss.setAudioCodec(confirmUploadBo.getAudioCodec() != null ?
                    confirmUploadBo.getAudioCodec() : existingRecord.getAudioCodec());

                oss.setAspectRatio(confirmUploadBo.getAspectRatio() != null ?
                    confirmUploadBo.getAspectRatio() : existingRecord.getAspectRatio());

                oss.setCreatedDate(confirmUploadBo.getCreatedDate() != null ?
                    confirmUploadBo.getCreatedDate() : existingRecord.getCreatedDate());

                oss.setLastModifiedDate(confirmUploadBo.getLastModifiedDate() != null ?
                    confirmUploadBo.getLastModifiedDate() : existingRecord.getLastModifiedDate());
            } else {
                // 如果是新记录，直接使用回调传入的值
                oss.setVideoDurationSeconds(confirmUploadBo.getVideoDurationSeconds());
                oss.setVideoResolution(confirmUploadBo.getVideoResolution());
                oss.setVideoBitrateKbps(confirmUploadBo.getVideoBitrateKbps());
                oss.setFrameRate(confirmUploadBo.getFrameRate());
                oss.setVideoCodec(confirmUploadBo.getVideoCodec());
                oss.setAudioCodec(confirmUploadBo.getAudioCodec());
                oss.setAspectRatio(confirmUploadBo.getAspectRatio());
                oss.setCreatedDate(confirmUploadBo.getCreatedDate());
                oss.setLastModifiedDate(confirmUploadBo.getLastModifiedDate());
            }

            oss.setUploadStatus(UploadStatusEnum.COMPLETED.getCode()); // 已完成
            oss.setUploadProgressPercent(100L);
            oss.setUploadCompleteTime(java.util.Date.from(java.time.Instant.now()));

            boolean result;
            if (isUpdate) {
                result = liveVideoUploadService.updateByBo(oss);
            } else {
                result = liveVideoUploadService.insertByBo(oss);
            }

            if (!result) {
                throw new ServiceException("保存上传记录失败");
            }

            // 通过ID查询获取完整的VO对象
            LiveVideoUploadVo vo = liveVideoUploadService.queryById(oss.getId());
            if (vo == null) {
                throw new ServiceException("查询上传记录失败");
            }

            // 文件上传完成后，自动创建转码任务
            try {
                createTranscodeTask(confirmUploadBo, fileUrl, storage);
                log.info("转码任务创建成功，liveId: {}, objectKey: {}",
                    confirmUploadBo.getLiveId(), confirmUploadBo.getObjectKey());
            } catch (Exception e) {
                log.error("创建转码任务失败，但文件上传已完成", e);
                // 转码任务创建失败不影响文件上传的成功状态
            }
            return this.matchingUrl(vo);

        } catch (Exception e) {
            log.error("确认上传失败", e);
            throw new ServiceException("确认上传失败: " + e.getMessage());
        }
    }

    /**
     * 创建转码任务并调用阿里云函数计算转码
     */
    private void createTranscodeTask(ConfirmUploadBo confirmUploadBo, String fileUrl, OssClient storage) {
        try {
            // 生成任务编号
            String taskNo = "TASK" + System.currentTimeMillis();
            String bizId = java.util.UUID.randomUUID().toString();

            // 创建转码任务记录
            LiveTranscodeTaskBo transcodeTask = new LiveTranscodeTaskBo();
            transcodeTask.setTaskNo(taskNo);
            transcodeTask.setTaskType("fc"); // 使用函数计算转码 这里判断一下 小文件使用fc转码 大文件使用mts转码
            transcodeTask.setLiveId(confirmUploadBo.getLiveId());
            transcodeTask.setSourceUrl(fileUrl);
            transcodeTask.setStatus(0L); // 待处理
            transcodeTask.setBucket(getBucketName(storage));
            transcodeTask.setObjectKey(confirmUploadBo.getObjectKey());
            transcodeTask.setFileSize(confirmUploadBo.getFileSize());
            transcodeTask.setDuration(confirmUploadBo.getVideoDurationSeconds() != null ? confirmUploadBo.getVideoDurationSeconds().longValue() : null);
            transcodeTask.setBizId(bizId);
            transcodeTask.setCallbackUrl("https://yd-live.vip.cpolar.cn/live/transcode/callback"); // 转码完成回调地址
            transcodeTask.setCallbackStatus(0L); // 未回调
            transcodeTask.setProgress(0L);
            transcodeTask.setRetryCount(0L);
            transcodeTask.setPriority(5L); // 默认优先级
            transcodeTask.setStartTime(new java.util.Date());

            // 保存转码任务到数据库
            boolean taskCreated = liveTranscodeTaskService.insertByBo(transcodeTask);
            if (!taskCreated) {
                throw new ServiceException("转码任务记录创建失败");
            }

            // 调用阿里云函数计算转码服务
            callTranscodeService(confirmUploadBo, bizId, storage);

            // 更新任务状态为处理中
            transcodeTask.setStatus(1L); // 处理中
            liveTranscodeTaskService.updateByBo(transcodeTask);

        } catch (Exception e) {
            log.error("创建转码任务失败", e);
            throw new ServiceException("创建转码任务失败: " + e.getMessage());
        }
    }

    private void callTranscodeService(ConfirmUploadBo confirmUploadBo, String bizId, OssClient storage) {
        if (confirmUploadBo.getTranscodeTemplateIds() == null || confirmUploadBo.getTranscodeTemplateIds().isEmpty()) {
            log.info("没有提供转码模板ID，跳过转码服务调用。BizId: {}", bizId);
            return;
        }

        try {
            // 1. 根据ID查询转码模板实体
            List<LiveTranscodeTemplate> templateEntities = liveTranscodeTemplateService.listByIds(confirmUploadBo.getTranscodeTemplateIds());
            if (templateEntities.isEmpty()) {
                throw new ServiceException("根据提供的ID未找到任何有效的转码模板");
            }

            // 2. 将模板实体转换为DTO
            List<TranscodeTemplateInfo> templateInfos = templateEntities.stream()
                .map(TranscodeTemplateConverter::toTemplateInfo)
                .collect(Collectors.toList());

            // 3. 构建转码请求
            TranscodeRequestVo request = new TranscodeRequestVo();
            request.setBucket(getBucketName(storage));
            request.setObject(confirmUploadBo.getObjectKey());
            request.setInputLocation(storage.of().id());
            request.setBizId(bizId);
            request.setCallbackUrl("https://yd-live.cpolar.cn/live/transcode/callback"); // Consider making this configurable
            request.setTemplates(templateInfos);
            request.setFileSize(confirmUploadBo.getFileSize());

            // 4. 调用统一转码服务
            TranscodeInitResponseVo response = universalTranscodeService.transcode(request);

            // 5. 创建并保存转码任务记录
            LiveTranscodeTaskBo taskBo = new LiveTranscodeTaskBo();
            taskBo.setLiveId(confirmUploadBo.getLiveId());
            taskBo.setTaskNo(UUID.randomUUID().toString());
            taskBo.setTaskType(response.getTranscodeType());
            taskBo.setSourceUrl(confirmUploadBo.getObjectKey());
            taskBo.setProviderJobId(response.getTaskId());
            taskBo.setBizId(bizId);
            taskBo.setStatus(1L); // 处理中
            taskBo.setErrorMsg(response.getErrorMessage());
            liveTranscodeTaskService.insertByBo(taskBo);

            log.info("转码任务已成功启动。BizId: {}, TaskId: {}, Method: {}",
                     bizId, response.getTaskId(), response.getTranscodeType());

        } catch (Exception e) {
            log.error("调用转码服务失败, BizId: {}", bizId, e);
            // Even if transcoding fails, we should record it
            LiveTranscodeTaskBo taskBo = new LiveTranscodeTaskBo();
            taskBo.setLiveId(confirmUploadBo.getLiveId());
            taskBo.setTaskNo(UUID.randomUUID().toString());
            taskBo.setSourceUrl(confirmUploadBo.getObjectKey());
            taskBo.setBizId(bizId);
            taskBo.setStatus(3L); // 已失败
            taskBo.setErrorMsg("启动转码失败: " + e.getMessage());
            liveTranscodeTaskService.insertByBo(taskBo);
        }
    }

    /**
     * 验证是否为有效的视频文件类型
     */
    private boolean isValidVideoType(String contentType) {
        if (contentType == null) {
            return false;
        }
        for (String allowedType : ALLOWED_VIDEO_TYPES) {
            if (allowedType.equals(contentType)) {
                return true;
            }
        }
        return false;
    }

    private String buildUploadPolicy(String objectKey, String contentType, Long maxFileSize, long expiration) {
        // 从objectKey提取文件名
        String fileName = objectKey.substring(objectKey.lastIndexOf("/") + 1);

        // 构建OSS回调参数
        Map<String, Object> callbackParams = new HashMap<>();
        callbackParams.put("callbackUrl", config.getOss().getCallbackUrl());

        // 扩展回调参数，包含更多视频信息
        callbackParams.put("callbackBody",
            "{\"objectKey\":${object},\"size\":${size},\"mimeType\":${mimeType},\"liveId\":${x:liveId}," +
            "\"fileName\":\"" + fileName + "\"," +
            "\"contentType\":\"" + contentType + "\"," +
            "\"videoDurationSeconds\":${x:videoDurationSeconds}," +
            "\"videoResolution\":${x:videoResolution}," +
            "\"videoBitrateKbps\":${x:videoBitrateKbps}," +
            "\"frameRate\":${x:frameRate}," +
            "\"videoCodec\":${x:videoCodec}," +
            "\"audioCodec\":${x:audioCodec}," +
            "\"aspectRatio\":${x:aspectRatio}," +
            "\"createdDate\":${x:createdDate}," +
            "\"lastModifiedDate\":${x:lastModifiedDate}}");

        callbackParams.put("callbackBodyType", "application/json");
        callbackParams.put("callbackSNI", true);  // 添加SNI支持，布尔值

        // 将回调参数转换为JSON格式并进行Base64编码
        String callbackJson = JSON.toJSONString(callbackParams);
        String callbackBase64 = java.util.Base64.getEncoder().encodeToString(callbackJson.getBytes());

        // 记录回调配置
        log.info("buildUploadPolicy中的OSS回调配置: {}", callbackJson);

        // 构建OSS PostObject的Policy JSON字符串
        String policyJson = String.format(
            "{\"expiration\":\"%s\",\"conditions\":[[\"eq\",\"$key\",\"%s\"],[\"eq\",\"$Content-Type\",\"%s\"],[\"content-length-range\",0,%d]]}",
            java.time.Instant.ofEpochSecond(expiration).toString(),
            objectKey,
            contentType,
            maxFileSize
        );

        // 将policy转换为Base64编码
        return java.util.Base64.getEncoder().encodeToString(policyJson.getBytes());
    }

    /**
     * 生成签名
     */
    private String generateSignature(String policy, OssClient storage) {
        try {
            String accessKeySecret = getAccessKeySecret(storage);

            // 使用HMAC-SHA1算法生成签名
            Mac mac = Mac.getInstance("HmacSHA1");
            SecretKeySpec secretKeySpec = new SecretKeySpec(
                accessKeySecret.getBytes(StandardCharsets.UTF_8), "HmacSHA1");
            mac.init(secretKeySpec);

            // 对policy进行HMAC-SHA1签名
            byte[] signatureBytes = mac.doFinal(policy.getBytes(StandardCharsets.UTF_8));

            // 将签名结果进行Base64编码
            return java.util.Base64.getEncoder().encodeToString(signatureBytes);

        } catch (NoSuchAlgorithmException | InvalidKeyException e) {
            log.error("生成OSS签名失败", e);
            throw new ServiceException("生成OSS签名失败: " + e.getMessage());
        }
    }

    /**
     * 获取AccessKeyId
     */
    private String getAccessKeyId(OssClient storage) {
        try {
            // 从缓存中获取OSS配置
            String configKey = storage.getConfigKey();
            String configJson = CacheUtils.get(CacheNames.SYS_OSS_CONFIG, configKey);

            if (configJson != null) {
                // 尝试从JSON中解析accessKey字段
                if (configJson.contains("\"accessKey\"")) {
                    int startIndex = configJson.indexOf("\"accessKey\":\"") + 13;
                    int endIndex = configJson.indexOf("\"", startIndex);
                    if (startIndex > 12 && endIndex > startIndex) {
                        String accessKey = configJson.substring(startIndex, endIndex);
                        if (!accessKey.isEmpty()) {
                            return accessKey;
                        }
                    }
                }
            }

            // 如果缓存中没有或解析失败，使用默认配置
            log.warn("无法从缓存获取OSS配置，使用默认AccessKeyId");
            return "LTAI5tGA15ASheoXwvc7pYcN";

        } catch (Exception e) {
            log.error("获取AccessKeyId失败", e);
            // 降级处理，使用默认值
            return "LTAI5tGA15ASheoXwvc7pYcN";
        }
    }

    /**
     * 获取AccessKeySecret
     */
    private String getAccessKeySecret(OssClient storage) {
        try {
            // 从缓存中获取OSS配置
            String configKey = storage.getConfigKey();
            String configJson = CacheUtils.get(CacheNames.SYS_OSS_CONFIG, configKey);

            if (configJson != null) {
                // 尝试从JSON中解析secretKey字段
                // 使用简单的字符串解析，避免依赖SysOssConfig类
                if (configJson.contains("\"secretKey\"")) {
                    int startIndex = configJson.indexOf("\"secretKey\":\"") + 13;
                    int endIndex = configJson.indexOf("\"", startIndex);
                    if (startIndex > 12 && endIndex > startIndex) {
                        String secretKey = configJson.substring(startIndex, endIndex);
                        if (!secretKey.isEmpty()) {
                            return secretKey;
                        }
                    }
                }
            }

            // 如果缓存中没有或解析失败，使用默认配置
            log.warn("无法从缓存获取OSS配置，使用默认AccessKeySecret");
            return "L7ZCeZs1sKUy1bBtxd6sIflyRiQNAf";

        } catch (Exception e) {
            log.error("获取AccessKeySecret失败", e);
            // 降级处理，使用默认值
            return "L7ZCeZs1sKUy1bBtxd6sIflyRiQNAf";
        }
    }

    /**
     * 获取Bucket名称（从OSS配置获取）
     */
    private String getBucketName(OssClient storage) {
        try {
            // 从缓存中获取OSS配置
            String configKey = storage.getConfigKey();
            String configJson = CacheUtils.get(CacheNames.SYS_OSS_CONFIG, configKey);

            if (configJson != null) {
                // 尝试从JSON中解析bucketName字段
                if (configJson.contains("\"bucketName\"")) {
                    int startIndex = configJson.indexOf("\"bucketName\":\"") + 14;
                    int endIndex = configJson.indexOf("\"", startIndex);
                    if (startIndex > 13 && endIndex > startIndex) {
                        String bucketName = configJson.substring(startIndex, endIndex);
                        if (!bucketName.isEmpty()) {
                            return bucketName;
                        }
                    }
                }
            }

            // 如果缓存中没有或解析失败，使用默认配置
            log.warn("无法从缓存获取OSS配置，使用默认BucketName");
            return "ydwl-live-recording"; // 默认bucket名称

        } catch (Exception e) {
            log.error("获取BucketName失败", e);
            // 降级处理，使用默认值
            return "ydwl-live-recording";
        }
    }

    /**
     * 获取Endpoint（从OSS配置获取）
     */
    private String getEndpoint(OssClient storage) {
        try {
            // 从缓存中获取OSS配置
            String configKey = storage.getConfigKey();
            String configJson = CacheUtils.get(CacheNames.SYS_OSS_CONFIG, configKey);

            if (configJson != null) {
                // 尝试从JSON中解析endpoint字段
                if (configJson.contains("\"endpoint\"")) {
                    int startIndex = configJson.indexOf("\"endpoint\":\"") + 12;
                    int endIndex = configJson.indexOf("\"", startIndex);
                    if (startIndex > 11 && endIndex > startIndex) {
                        String endpoint = configJson.substring(startIndex, endIndex);
                        if (!endpoint.isEmpty()) {
                            return endpoint;
                        }
                    }
                }
            }

            // 如果缓存中没有或解析失败，使用默认配置
            log.warn("无法从缓存获取OSS配置，使用默认Endpoint");
            return "oss-cn-beijing.aliyuncs.com"; // 默认endpoint

        } catch (Exception e) {
            log.error("获取Endpoint失败", e);
            // 降级处理，使用默认值
            return "oss-cn-beijing.aliyuncs.com";
        }
    }


    /**
     *
     * @param originalfileName 原始文件名
     * @param suffix 视频格式
     * @param configKey 存储bucket名称
     * @param uploadResult{ url,filename,Etag}
     * @return
     */
    @NotNull
    private LiveVideoUploadVo buildResultEntity(String originalfileName, String suffix, String configKey, UploadResult uploadResult, long id) {
        LiveVideoUploadBo oss = new LiveVideoUploadBo();
        oss.setOssUrl(uploadResult.getUrl());
        oss.setFileType(suffix);
        oss.setFileName(originalfileName);
        oss.setLiveId(id);
        liveVideoUploadService.insertByBo(oss);
        LiveVideoUploadVo vo = MapstructUtils.convert(oss, LiveVideoUploadVo.class);
        return this.matchingUrl(vo);
    }


    /**
     * 桶类型为 private 的URL 修改为临时URL时长为120s
     *
     * @param oss OSS对象
     * @return oss 匹配Url的OSS对象
     */
    private LiveVideoUploadVo matchingUrl(LiveVideoUploadVo oss) {
        OssClient storage = OssFactory.instance("aliyun-video-ydwl");
        // 仅修改桶类型为 private 的URL，临时URL时长为120s
        if (AccessPolicyType.PRIVATE == storage.getAccessPolicy()) {
            oss.setOssUrl(storage.getPrivateUrl(oss.getFileName(), Duration.ofSeconds(120)));
        }
        return oss;
    }


    private final OssProperties ossProperties;
    private final OSS ossClient;

    /**
     * 生成CDN鉴权签名URL
     *
     * @param url 原始URL
     * @param expiredTime 过期时间（秒）
     * @param allowedExtensions 允许的文件扩展名列表
     * @return 签名后的URL
     */
    private String generateCdnSignedUrl(String url, long expiredTime, String... allowedExtensions) {
        if (StringUtils.isEmpty(url)) {
            throw new ServiceException("URL不能为空");
        }
        if (expiredTime <= 0 || expiredTime > 86400) {
            throw new ServiceException("无效的过期时间");
        }

        try {
            String cdnDomain = ossProperties.getCdnDomain();
            String objectPath = url.substring(url.indexOf("/live-videos/"));

            // 文件类型验证
            if (allowedExtensions != null && allowedExtensions.length > 0) {
                boolean isValidExtension = false;
                String lowerPath = objectPath.toLowerCase();
                for (String ext : allowedExtensions) {
                    if (lowerPath.endsWith(ext.toLowerCase())) {
                        isValidExtension = true;
                        break;
                    }
                }
                if (!isValidExtension) {
                    throw new ServiceException("非法的文件类型");
                }
            }

            // 设置签名过期时间
            long expirationTimeInSeconds = System.currentTimeMillis() / 1000 + expiredTime;

            // 构建待签名字符串 (CDN 鉴权方式 A)
            String stringToSign = objectPath + "-" + expirationTimeInSeconds;

            // 使用MD5计算签名
            MessageDigest md = MessageDigest.getInstance("MD5");
            String signStr = stringToSign + "-0-0-" + ossProperties.getCdnAccessKey();
            byte[] signatureBytes = md.digest(signStr.getBytes(StandardCharsets.UTF_8));

            // 转换为十六进制
            StringBuilder hexString = new StringBuilder();
            for (byte b : signatureBytes) {
                String hex = Integer.toHexString(0xff & b);
                if (hex.length() == 1) {
                    hexString.append('0');
                }
                hexString.append(hex);
            }
            String signature = hexString.toString();

            // 构建最终的URL（使用CDN鉴权方式A）
            String signedUrl = String.format("https://%s%s?auth_key=%d-0-0-%s",
                cdnDomain,
                objectPath,
                expirationTimeInSeconds,
                signature);

            if (log.isDebugEnabled()) {
                log.debug("Generated signed URL for path: {}", objectPath);
            }

            return signedUrl;

        } catch (ServiceException e) {
            throw e;
        } catch (Exception e) {
            log.error("生成签名URL失败: {}", e.getMessage());
            throw new ServiceException("生成签名URL失败，请稍后重试");
        }
    }

    @Override
    public String getM3u8SignUrl(String url, long expiredTime) {
        return generateCdnSignedUrl(url, expiredTime, ".m3u8");
    }

    @Override
    public String getCoverSignUrl(String url) {
        // 图片URL默认1小时过期
        return generateCdnSignedUrl(url, 3600, ".jpg", ".jpeg", ".png", ".gif");
    }
}
