package com.ydwl.live.service.impl;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.LambdaUpdateWrapper;
import com.ydwl.live.domain.Live;
import com.ydwl.live.domain.LiveStream;
import com.ydwl.live.domain.bo.LiveBo;
import com.ydwl.live.mapper.LiveMapper;
import com.ydwl.live.mapper.LiveStreamMapper;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.Date;

/**
 * 直播状态管理器
 * 负责处理直播状态转换和相关业务逻辑
 *
 * <AUTHOR>
 * @date 2025-06-05
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class LiveStateManager {

    private final LiveMapper liveMapper;
    private final LiveStreamMapper liveStreamMapper;

    /**
     * 直播状态枚举
     */
    public enum LiveStatus {
        /**
         * 未开始
         */
        NOT_STARTED(0L),
        /**
         * 直播中
         */
        LIVE(1L),
        /**
         * 已结束
         */
        ENDED(2L),
        /**
         * 异常
         */
        ERROR(3L),
        /**
         * 回放
         */
        REPLAY(4L);

        private final Long value;

        LiveStatus(Long value) {
            this.value = value;
        }

        public Long getValue() {
            return value;
        }
    }

    /**
     * 更新直播状态
     *
     * @param liveId 直播ID
     * @param status 目标状态
     * @return 是否更新成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean updateLiveStatus(Long liveId, LiveStatus status) {
        if (liveId == null || status == null) {
            log.error("更新直播状态失败：参数不能为空");
            return false;
        }

        try {
            // 更新直播主表状态
            LambdaUpdateWrapper<Live> updateWrapper = new LambdaUpdateWrapper<>();
            updateWrapper.eq(Live::getId, liveId)
                .set(Live::getStatus, status.getValue())
                .set(Live::getUpdateTime, new Date());

            // 如果是开始直播，设置开始时间
            if (status == LiveStatus.LIVE) {
                updateWrapper.set(Live::getActualStartTime, new Date());
            } 
            // 如果是结束直播，设置结束时间
            else if (status == LiveStatus.ENDED) {
                updateWrapper.set(Live::getActualEndTime, new Date());
            }

            int result = liveMapper.update(null, updateWrapper);
            log.info("直播状态更新：liveId={}, status={}, 影响行数={}", liveId, status, result);
            
            return result > 0;
        } catch (Exception e) {
            log.error("更新直播状态异常：liveId={}, status={}", liveId, status, e);
            throw e;
        }
    }

    /**
     * 检查直播状态
     *
     * @param liveId 直播ID
     * @return 当前状态
     */
    public LiveStatus checkLiveStatus(Long liveId) {
        if (liveId == null) {
            return LiveStatus.ERROR;
        }

        try {
            Live live = liveMapper.selectOne(new LambdaQueryWrapper<Live>()
                .eq(Live::getId, liveId)
                .select(Live::getStatus));

            if (live == null) {
                log.warn("直播不存在：liveId={}", liveId);
                return LiveStatus.ERROR;
            }

            Long statusValue = live.getStatus();
            for (LiveStatus status : LiveStatus.values()) {
                if (status.getValue().equals(statusValue)) {
                    return status;
                }
            }

            log.warn("未知的直播状态值：liveId={}, status={}", liveId, statusValue);
            return LiveStatus.ERROR;
        } catch (Exception e) {
            log.error("检查直播状态异常：liveId={}", liveId, e);
            return LiveStatus.ERROR;
        }
    }

    /**
     * 检查直播推流状态
     *
     * @param liveId 直播ID
     * @return 推流状态值
     */
    public Long checkStreamStatus(Long liveId) {
        if (liveId == null) {
            return 2L; // 异常状态
        }

        try {
            LiveStream stream = liveStreamMapper.selectOne(new LambdaQueryWrapper<LiveStream>()
                .eq(LiveStream::getLiveId, liveId)
                .select(LiveStream::getStreamStatus));

            return stream != null ? stream.getStreamStatus() : 2L;
        } catch (Exception e) {
            log.error("检查推流状态异常：liveId={}", liveId, e);
            return 2L;
        }
    }

    /**
     * 获取当前直播状态
     *
     * @param liveId 直播ID
     * @return 当前状态
     */
    public LiveStatus getCurrentStatus(Long liveId) {
        return checkLiveStatus(liveId);
    }

    /**
     * 初始化直播状态
     *
     * @param liveId 直播ID
     * @return 是否初始化成功
     */
    @Transactional(rollbackFor = Exception.class)
    public boolean initializeLiveState(Long liveId) {
        if (liveId == null) {
            log.error("初始化直播状态失败：liveId不能为空");
            return false;
        }

        try {
            // 设置直播状态为未开始
            boolean result = updateLiveStatus(liveId, LiveStatus.NOT_STARTED);
            log.info("初始化直播状态：liveId={}, 结果={}", liveId, result);
            return result;
        } catch (Exception e) {
            log.error("初始化直播状态异常：liveId={}", liveId, e);
            return false;
        }
    }

    /**
     * 检查状态转换是否允许
     *
     * @param from 源状态
     * @param to   目标状态
     * @return 是否允许转换
     */
    public boolean canTransitionTo(LiveStatus from, LiveStatus to) {
        if (from == null || to == null) {
            return false;
        }

        // 定义状态转换规则
        switch (from) {
            case NOT_STARTED:
                return to == LiveStatus.LIVE || to == LiveStatus.ERROR;
            case LIVE:
                return to == LiveStatus.ENDED || to == LiveStatus.ERROR || to == LiveStatus.REPLAY;
            case ENDED:
                return to == LiveStatus.REPLAY || to == LiveStatus.ERROR;
            case REPLAY:
                return to == LiveStatus.ERROR;
            case ERROR:
                return to == LiveStatus.NOT_STARTED || to == LiveStatus.LIVE;
            default:
                return false;
        }
    }
}