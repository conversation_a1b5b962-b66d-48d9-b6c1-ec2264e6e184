package com.ydwl.live.service.impl;

import com.ydwl.common.core.service.M3u8Service;
import com.ydwl.common.core.utils.MapstructUtils;
import com.ydwl.common.core.utils.StringUtils;
import com.ydwl.common.mybatis.core.page.TableDataInfo;
import com.ydwl.common.mybatis.core.page.PageQuery;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import com.ydwl.live.domain.bo.LiveReplayBo;
import com.ydwl.live.domain.vo.LiveReplayVo;
import com.ydwl.live.domain.LiveReplay;
import com.ydwl.live.mapper.LiveReplayMapper;
import com.ydwl.live.service.ILiveReplayService;

import java.time.Duration;
import java.util.*;
import java.util.Map;
import java.util.stream.Collectors;
/**
 * 直播回放Service业务层处理
 *
 * <AUTHOR> Yi
 * @date 2025-05-21
 */
@Slf4j
@RequiredArgsConstructor
@Service
public class LiveReplayServiceImpl implements ILiveReplayService {

    private final LiveReplayMapper baseMapper;

    private final M3u8Service m3u8Service;

    /**
     * 查询直播回放
     *
     * @param id 主键
     * @return 直播回放
     */
    @Override
    public LiveReplayVo queryById(Long id){
        return baseMapper.selectVoById(id);
    }

    /**
     * 分页查询直播回放列表
     *
     * @param bo        查询条件
     * @param pageQuery 分页参数
     * @return 直播回放分页列表
     */
    @Override
    public TableDataInfo<LiveReplayVo> queryPageList(LiveReplayBo bo, PageQuery pageQuery) {
        LambdaQueryWrapper<LiveReplay> lqw = buildQueryWrapper(bo);
        Page<LiveReplayVo> result = baseMapper.selectVoPage(pageQuery.build(), lqw);

        // 处理回放URL，添加动态签名
        List<LiveReplayVo> processedList = result.getRecords().stream()
            .map(this::matchingUrl)
            .collect(Collectors.toList());

        result.setRecords(processedList);
        return TableDataInfo.build(result);
    }

    /**
     * 查询符合条件的直播回放列表
     *
     * @param bo 查询条件
     * @return 直播回放列表
     */
    @Override
    public List<LiveReplayVo> queryList(LiveReplayBo bo) {
        LambdaQueryWrapper<LiveReplay> lqw = buildQueryWrapper(bo);
        List<LiveReplayVo> list = baseMapper.selectVoList(lqw);

        // 处理回放URL，添加动态签名
        return list.stream()
            .map(this::matchingUrl)
            .collect(Collectors.toList());
    }

    /**
     * 为回放URL添加动态签名
     *
     * @param replay 回放对象
     * @return 处理后的回放对象
     */
    private LiveReplayVo matchingUrl(LiveReplayVo replay) {
        if (replay == null || StringUtils.isEmpty(replay.getReplayUrl())) {
            return replay;
        }

        try {
            // 使用M3u8Service来获取签名URL
            String signedUrl = m3u8Service.getM3u8SignUrl(replay.getReplayUrl(), (int) Duration.ofHours(24).toSeconds());
            if (StringUtils.isNotEmpty(signedUrl)) {
                replay.setReplayUrl(signedUrl);
                log.info("生成签名URL: {}, 原始URL: {}", replay.getReplayUrl(), replay.getReplayUrl());
            }
        } catch (Exception e) {
            log.error("处理回放URL动态签名失败: {}", e.getMessage(), e);
            // 出错时保留原始URL
        }

        return replay;
    }

    /**
     * 计算OSS签名
     *
     * @param bo 访问密钥密码
     * @return 计算出的签名（Base64编码）
     * @throws Exception 如果签名过程发生错误
     */


    private LambdaQueryWrapper<LiveReplay> buildQueryWrapper(LiveReplayBo bo) {
        Map<String, Object> params = bo.getParams();
        LambdaQueryWrapper<LiveReplay> lqw = Wrappers.lambdaQuery();
        lqw.orderByAsc(LiveReplay::getId);
        lqw.eq(bo.getLiveId() != null, LiveReplay::getLiveId, bo.getLiveId());
        lqw.eq(bo.getTranscodeTaskId() != null, LiveReplay::getTranscodeTaskId, bo.getTranscodeTaskId());
        lqw.eq(StringUtils.isNotBlank(bo.getReplayUrl()), LiveReplay::getReplayUrl, bo.getReplayUrl());
        lqw.eq(bo.getStatus() != null, LiveReplay::getStatus, bo.getStatus());
        lqw.eq(bo.getDuration() != null, LiveReplay::getDuration, bo.getDuration());
        lqw.eq(bo.getAvailableTime() != null, LiveReplay::getAvailableTime, bo.getAvailableTime());
        lqw.eq(bo.getExpiryTime() != null, LiveReplay::getExpiryTime, bo.getExpiryTime());
        lqw.eq(bo.getAccessType() != null, LiveReplay::getAccessType, bo.getAccessType());
        lqw.eq(bo.getViewCount() != null, LiveReplay::getViewCount, bo.getViewCount());
        return lqw;
    }

    /**
     * 新增直播回放
     *
     * @param bo 直播回放
     * @return 是否新增成功
     */
    @Override
    public Boolean insertByBo(LiveReplayBo bo) {
        LiveReplay add = MapstructUtils.convert(bo, LiveReplay.class);
        validEntityBeforeSave(add);
        boolean flag = baseMapper.insert(add) > 0;
        if (flag) {
            bo.setId(add.getId());
        }
        return flag;
    }

    /**
     * 修改直播回放
     *
     * @param bo 直播回放
     * @return 是否修改成功
     */
    @Override
    public Boolean updateByBo(LiveReplayBo bo) {
        LiveReplay update = MapstructUtils.convert(bo, LiveReplay.class);
        validEntityBeforeSave(update);
        return baseMapper.updateById(update) > 0;
    }

    /**
     * 保存前的数据校验
     */
    private void validEntityBeforeSave(LiveReplay entity){
        //TODO 做一些数据校验,如唯一约束
    }

    /**
     * 校验并批量删除直播回放信息
     *
     * @param ids     待删除的主键集合
     * @param isValid 是否进行有效性校验
     * @return 是否删除成功
     */
    @Override
    public Boolean deleteWithValidByIds(Collection<Long> ids, Boolean isValid) {
        if(isValid){
            //TODO 做一些业务上的校验,判断是否需要校验
        }
        return baseMapper.deleteByIds(ids) > 0;
    }

    /**
     * 根据直播ID列表批量查询直播回放信息
     *
     * @param liveIds 直播ID列表
     * @return 直播ID到直播回放列表的映射
     */
    @Override
    public Map<Long, List<LiveReplayVo>> queryListMapByLiveIds(List<Long> liveIds) {
        Map<Long, List<LiveReplayVo>> resultMap = new HashMap<>();
        if (liveIds == null || liveIds.isEmpty()) {
            return resultMap;
        }

        // 批量查询回放信息
        LambdaQueryWrapper<LiveReplay> queryWrapper = Wrappers.lambdaQuery();
        queryWrapper.in(LiveReplay::getLiveId, liveIds);
        List<LiveReplay> replayList = baseMapper.selectList(queryWrapper);

        // 按liveId分组
        Map<Long, List<LiveReplayVo>> groupedReplays = replayList.stream()
            .map(replay -> MapstructUtils.convert(replay, LiveReplayVo.class))
            .collect(Collectors.groupingBy(LiveReplayVo::getLiveId));

        // 为每个liveId填充结果，并处理URL签名
        for (Long liveId : liveIds) {
            List<LiveReplayVo> replaysForLive = groupedReplays.getOrDefault(liveId, new ArrayList<>());
            List<LiveReplayVo> processedReplays = replaysForLive.stream()
                .map(this::matchingUrl)
                .collect(Collectors.toList());
            resultMap.put(liveId, processedReplays);
        }

        return resultMap;
    }

    /**
     * 根据直播ID查询直播回放列表
     *
     * @param liveId 直播ID
     * @return 直播回放列表
     */
    @Override
    public List<LiveReplayVo> queryListByLiveId(Long liveId) {
        if (liveId == null) {
            return new ArrayList<>();
        }

        LiveReplayBo bo = new LiveReplayBo();
        bo.setLiveId(liveId);
        return queryList(bo);
    }
}
