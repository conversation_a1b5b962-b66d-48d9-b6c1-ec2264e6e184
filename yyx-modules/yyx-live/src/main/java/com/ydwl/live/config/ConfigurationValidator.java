package com.ydwl.live.config;

import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.boot.context.event.ApplicationReadyEvent;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Component;

/**
 * 配置验证器
 * 在应用启动后验证所有关键配置是否正确
 *
 * <AUTHOR>
 * @date 2025-06-10
 */
@Slf4j
@Component
@RequiredArgsConstructor
public class ConfigurationValidator {

    private final ConfigManager configManager;

    /**
     * 应用启动后验证配置
     */
    @EventListener(ApplicationReadyEvent.class)
    public void validateConfigurations() {
        log.info("开始验证应用配置...");
        
        boolean allValid = true;
        
        // 验证OSS配置
        allValid &= validateOssConfig();
        
        // 验证阿里云配置
        allValid &= validateAliyunConfig();
        
        // 验证微信小程序配置
        allValid &= validateWxMiniappConfig();
        
        // 验证推流配置
        allValid &= validateStreamConfig();
        
        if (allValid) {
            log.info("✅ 所有配置验证通过");
        } else {
            log.error("❌ 配置验证失败，请检查上述错误信息");
        }
    }

    /**
     * 验证OSS配置
     */
    private boolean validateOssConfig() {
        try {
            UnifiedLiveConfig.OssConfig ossConfig = configManager.getOssConfig();
            if (ossConfig == null) {
                log.error("❌ OSS配置为空");
                return false;
            }
            
            boolean valid = true;
            
            if (isEmpty(ossConfig.getAccessKey())) {
                log.error("❌ OSS Access Key为空");
                valid = false;
            }
            
            if (isEmpty(ossConfig.getSecretKey())) {
                log.error("❌ OSS Secret Key为空");
                valid = false;
            }
            
            if (isEmpty(ossConfig.getEndpoint())) {
                log.error("❌ OSS Endpoint为空");
                valid = false;
            }
            
            if (isEmpty(ossConfig.getBucketName())) {
                log.error("❌ OSS Bucket Name为空");
                valid = false;
            }
            
            if (valid) {
                log.info("✅ OSS配置验证通过");
            }
            
            return valid;
        } catch (Exception e) {
            log.error("❌ OSS配置验证异常", e);
            return false;
        }
    }

    /**
     * 验证阿里云配置
     */
    private boolean validateAliyunConfig() {
        try {
            UnifiedLiveConfig.AliyunConfig aliyunConfig = configManager.getAliyunConfig();
            if (aliyunConfig == null) {
                log.error("❌ 阿里云配置为空");
                return false;
            }
            
            boolean valid = true;
            
            if (isEmpty(aliyunConfig.getAccessKeyId())) {
                log.error("❌ 阿里云 Access Key ID为空");
                valid = false;
            }
            
            if (isEmpty(aliyunConfig.getAccessKeySecret())) {
                log.error("❌ 阿里云 Access Key Secret为空");
                valid = false;
            }
            
            if (isEmpty(aliyunConfig.getRegionId())) {
                log.error("❌ 阿里云 Region ID为空");
                valid = false;
            }
            
            if (valid) {
                log.info("✅ 阿里云配置验证通过");
            }
            
            return valid;
        } catch (Exception e) {
            log.error("❌ 阿里云配置验证异常", e);
            return false;
        }
    }

    /**
     * 验证微信小程序配置
     */
    private boolean validateWxMiniappConfig() {
        try {
            UnifiedLiveConfig.WxMiniappConfig wxConfig = configManager.getWxMiniappConfig();
            if (wxConfig == null) {
                log.warn("⚠️ 微信小程序配置为空（可选配置）");
                return true; // 微信小程序配置是可选的
            }
            
            boolean valid = true;
            
            if (isEmpty(wxConfig.getAppid()) || "你的appid".equals(wxConfig.getAppid())) {
                log.warn("⚠️ 微信小程序 AppID未配置或使用默认值");
                valid = false;
            }
            
            if (isEmpty(wxConfig.getSecret()) || "你的app secret".equals(wxConfig.getSecret())) {
                log.warn("⚠️ 微信小程序 Secret未配置或使用默认值");
                valid = false;
            }
            
            if (valid) {
                log.info("✅ 微信小程序配置验证通过");
            } else {
                log.warn("⚠️ 微信小程序配置不完整，相关功能可能无法使用");
            }
            
            return true; // 微信小程序配置不完整不影响应用启动
        } catch (Exception e) {
            log.error("❌ 微信小程序配置验证异常", e);
            return false;
        }
    }

    /**
     * 验证推流配置
     */
    private boolean validateStreamConfig() {
        try {
            UnifiedLiveConfig.StreamConfig streamConfig = configManager.getStreamConfig();
            if (streamConfig == null) {
                log.error("❌ 推流配置为空");
                return false;
            }
            
            boolean valid = true;
            
            if (isEmpty(streamConfig.getPushDomain())) {
                log.error("❌ 推流域名为空");
                valid = false;
            }
            
            if (isEmpty(streamConfig.getPlayDomain())) {
                log.error("❌ 播放域名为空");
                valid = false;
            }
            
            if (isEmpty(streamConfig.getAppName())) {
                log.error("❌ 应用名称为空");
                valid = false;
            }
            
            if (valid) {
                log.info("✅ 推流配置验证通过");
            }
            
            return valid;
        } catch (Exception e) {
            log.error("❌ 推流配置验证异常", e);
            return false;
        }
    }

    /**
     * 检查字符串是否为空
     */
    private boolean isEmpty(String str) {
        return str == null || str.trim().isEmpty();
    }
}
